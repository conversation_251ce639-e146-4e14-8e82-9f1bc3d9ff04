{"name": "app-hero", "version": "1.0.0", "private": true, "description": "", "main": "index.js", "workspaces": ["apps/*", "libs/*", "packages/*"], "scripts": {"android:apk": "yarn workspace mobile android:apk", "android:bundle": "yarn workspace mobile android:bundle", "android:clean": "yarn workspace mobile android:clean", "android": "yarn workspace mobile android", "clean": "node ./scripts/clean.ts", "commit": "git-cz", "desktop": "yarn workspace desktop start", "format": "prettier --write .", "ios:build": "yarn workspace mobile ios:build", "ios": "yarn workspace mobile ios", "lint": "eslint .", "pod": "cd apps/mobile/ios && pod update && pod install && cd -", "prettier": "prettier --write \"**/*.{js,json,md,yml}\"", "start": "env-cmd -f .env.development yarn workspace mobile start --reset-cache", "start:dev": "env-cmd -f .env.development yarn workspace mobile start --reset-cache", "start:prod": "env-cmd -f .env.production yarn workspace mobile start --reset-cache", "test": "echo \"Error: no test specified\" && exit 1", "web": "env-cmd -f .env.development yarn workspace web start", "web:build": "env-cmd -f .env.development yarn workspace web build", "web:dev": "env-cmd -f .env.development yarn workspace web start", "web:prod": "env-cmd -f .env.production yarn workspace web start", "web:build:dev": "env-cmd -f .env.development yarn workspace web build", "web:build:prod": "env-cmd -f .env.production yarn workspace web build", "xcode": "yarn workspace mobile xcode"}, "dependencies": {"@apollo/client": "^3.10.3", "@react-native-async-storage/async-storage": "^1.19.1", "@react-native-community/clipboard": "^1.5.1", "@react-native-community/netinfo": "^9.4.1", "@react-native-masked-view/masked-view": "^0.2.9", "@react-navigation/native": "^6.1.7", "@react-navigation/native-stack": "^6.9.13", "@react-navigation/stack": "^6.3.17", "@sentry/react-native": "^5.7.1", "@tanstack/react-query": "^4.32.1", "@tanstack/react-query-devtools": "4", "@twotalltotems/react-native-otp-input": "^1.3.11", "aws-amplify": "^5.3.5", "babel-plugin-transform-remove-console": "^6.9.4", "crypto-js": "^4.2.0", "customize-cra": "1.0.0", "env-cmd": "^10.1.0", "expo-blur": "^12.9.2", "graphql": "^16.8.1", "graphql-subscriptions-client": "^0.16.4", "i18next": "^23.3.0", "jotai": "^2.2.2", "libphonenumber-js": "^1.11.1", "lucide-react": "^0.446.0", "moment": "^2.29.4", "query-string": "v8.2.0", "react": "18.2.0", "react-datepicker": "^8.2.1", "react-dom": "18.2.0", "react-i18next": "^13.0.2", "react-native": "0.71.8", "react-native-collapsible": "^1.6.1", "react-native-inappbrowser-reborn": "^3.7.0", "react-native-linear-gradient": "^2.8.3", "react-native-localize": "^3.0.2", "react-native-safe-area-context": "^4.7.1", "react-native-toast-notifications": "^3.4.0", "react-native-video": "^5.2.1", "react-native-web": "0.18.12", "react-native-web-linear-gradient": "^1.1.2", "react-native-web-lottie": "^1.4.4", "react-native-web-swiper": "^2.2.4", "react-native-webview": "^13.6.0", "react-scripts": "5.0.1", "rn-fetch-blob": "^0.12.0", "styled-components": "^6.0.8", "subscriptions-transport-ws": "^0.11.0", "translate-google-api": "^1.0.4", "uuid": "^11.1.0", "web-vitals": "2.1.4", "zen-observable": "^0.10.0"}, "devDependencies": {"@app-hero/native-icons": "1.0.0", "@app-hero/shared-hooks": "1.0.0", "@app-hero/shared-services": "1.0.0", "@babel/core": "7.22.6", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-transform-flow-strip-types": "^7.22.5", "@babel/plugin-transform-react-jsx": "^7.22.15", "@babel/preset-env": "7.22.6", "@babel/runtime": "7.22.6", "@electron-forge/cli": "6.0.0-beta.70", "@react-native-community/eslint-config": "3.2.0", "@testing-library/jest-dom": "5.16.5", "@testing-library/react": "13.4.0", "@tsconfig/react-native": "2.0.2", "@types/jest": "29.2.1", "@types/react": "18.0.24", "@types/react-test-renderer": "18.0.0", "babel-jest": "29.2.1", "babel-plugin-react-native-web": "0.18.12", "commitizen": "^4.3.0", "concurrently": "7.5.0", "cross-env": "7.0.3", "electron": "21.2.0", "electronmon": "2.0.2", "eslint": "^8.45.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.9.0", "eslint-plugin-ft-flow": "^2.0.3", "eslint-plugin-import": "^2.28.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "jest": "29.2.1", "metro-react-native-babel-preset": "0.73.9", "prettier": "2.4.1", "react-native-gesture-handler": "^2.12.0", "react-native-reanimated": "3.0.1", "react-native-screens": "^3.23.0", "react-test-renderer": "18.2.0", "typescript": "4.8.4", "wait-on": "6.0.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "keywords": [], "author": "", "license": "ISC"}