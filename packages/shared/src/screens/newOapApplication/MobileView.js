import React, { useState, useEffect, useRef } from 'react'
import {
  StyleSheet,
  TouchableOpacity,
  View,
  ScrollView,
  useWindowDimensions,
  Platform,
  Animated,
} from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { isTablet, isWeb } from '@libs/utils/src/screenLayout'
import { spacing, useTheme } from '@libs/theme'
import { Text, ShimmerPlaceholder } from '@libs/components'
import { Icon } from '@app-hero/native-icons'
import { OapPortal } from '../../components'
import { TitleHeader } from '../../components/headerTitle'
import { getOAPUrl } from '../../utils/getOAPUrl'

// Mobile Loading Component
const MobileApplicationLoadingState = () => {
  const { colors } = useTheme()
  const pulseAnim = useRef(new Animated.Value(0.4)).current
  const slideAnim = useRef(new Animated.Value(-30)).current
  const rotateAnim = useRef(new Animated.Value(0)).current

  useEffect(() => {
    // Pulse animation for the main icon
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 0.4,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
    )

    // Slide animation for progress dots
    const slideAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(slideAnim, {
          toValue: 30,
          duration: 1800,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: -30,
          duration: 1800,
          useNativeDriver: true,
        }),
      ]),
    )

    // Rotation animation for the outer ring
    const rotateAnimation = Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 2500,
        useNativeDriver: true,
      }),
    )

    pulseAnimation.start()
    slideAnimation.start()
    rotateAnimation.start()

    return () => {
      pulseAnimation.stop()
      slideAnimation.stop()
      rotateAnimation.stop()
    }
  }, [])

  const spin = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  })

  return (
    <View style={mobileLoadingStyles.container}>
      <View style={mobileLoadingStyles.mainContainer}>
        {/* Outer rotating ring */}
        <Animated.View
          style={[
            mobileLoadingStyles.outerRing,
            {
              borderColor: colors.primary,
              transform: [{ rotate: spin }],
            },
          ]}
        />

        {/* Main pulsing icon */}
        <Animated.View
          style={[
            mobileLoadingStyles.iconContainer,
            {
              opacity: pulseAnim,
              backgroundColor: colors.primaryContainer,
            },
          ]}
        >
          <Icon
            name="AuthLoading"
            style={[mobileLoadingStyles.icon, { tintColor: colors.primary }]}
          />
        </Animated.View>

        {/* Progress dots */}
        <View style={mobileLoadingStyles.dotsContainer}>
          {[0, 1, 2].map((index) => (
            <Animated.View
              key={index}
              style={[
                mobileLoadingStyles.dot,
                {
                  backgroundColor: colors.primary,
                  transform: [
                    {
                      translateX: Animated.add(
                        slideAnim,
                        new Animated.Value(index * 10),
                      ),
                    },
                  ],
                  opacity: pulseAnim,
                },
              ]}
            />
          ))}
        </View>
      </View>

      {/* Loading text with shimmer effect */}
      <View style={mobileLoadingStyles.textContainer}>
        <Text
          variant="heading4"
          style={[mobileLoadingStyles.loadingText, { color: colors.primary }]}
        >
          Preparing Portal
        </Text>
        <ShimmerPlaceholder
          style={[
            mobileLoadingStyles.shimmerBar,
            { backgroundColor: colors.primaryContainer },
          ]}
          shimmerColors={[
            colors.primaryContainer,
            colors.primary,
            colors.primaryContainer,
          ]}
        />
        <Text
          variant="caption"
          style={[mobileLoadingStyles.subText, { color: colors.onSurface }]}
        >
          Setting up connection...
        </Text>
      </View>
    </View>
  )
}

const mobileLoadingStyles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 24,
  },
  mainContainer: {
    position: 'relative',
    width: 120,
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
  },
  outerRing: {
    position: 'absolute',
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 2,
    borderStyle: 'dashed',
  },
  iconContainer: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.25,
    shadowRadius: 6,
  },
  icon: {
    width: 40,
    height: 40,
  },
  dotsContainer: {
    position: 'absolute',
    bottom: -25,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginHorizontal: 3,
  },
  textContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    textAlign: 'center',
    marginBottom: 12,
    fontWeight: '600',
  },
  shimmerBar: {
    width: 150,
    height: 3,
    borderRadius: 1.5,
    marginBottom: 8,
  },
  subText: {
    textAlign: 'center',
    opacity: 0.7,
  },
})

const MobileView = ({
  opportunities,
  isOpportunitiesFetching,
  scrollViewRef,
  tokens,
  isTokensFetching,
  updateOpportunity: updateOpportunityAfterSubmission,
  backText,
  isDashboard,
}) => {
  const { colors } = useTheme()
  const { width } = useWindowDimensions()
  const web = isWeb(width)
  const tablet = isTablet(width)
  const navigation = useNavigation()
  const [submissionConfirmed, setSubmissionConfirmed] = useState(false)
  const [countdown, setCountdown] = useState(30)

  // Auto-redirect countdown when submission is confirmed
  useEffect(() => {
    let interval = null
    if (submissionConfirmed) {
      interval = setInterval(() => {
        setCountdown((prevCountdown) => {
          if (prevCountdown <= 1) {
            // Redirect to my-application screen
            if (Platform.OS === 'web') {
              window.history.pushState({}, '', '/my-application')
              window.location.reload()
            } else {
              navigation.navigate('my-application')
            }
            return 0
          }
          return prevCountdown - 1
        })
      }, 1000)
    }

    return () => {
      if (interval) {
        clearInterval(interval)
      }
    }
  }, [submissionConfirmed, navigation])

  if (isOpportunitiesFetching) {
    return (
      <View
        style={{
          backgroundColor: '#fff',
          flexDirection: 'row',
          paddingHorizontal: 32,
          minHeight: '100vh',
        }}
      >
        <View
          style={{
            flexDirection: 'column',
            flex: 1,
            marginTop: 24,
          }}
        >
          <TouchableOpacity
            onPress={() => {
              if (Platform.OS === 'web') {
                if (isDashboard) {
                  window.history.pushState({}, '', '/dashboard')
                } else {
                  window.history.pushState({}, '', '/my-application')
                }
                window.location.reload()
              } else {
                navigation.navigate(
                  isDashboard ? 'dashboard' : 'my-application',
                )
              }
            }}
            style={{
              marginBottom: !tablet && spacing.spacing2,
              flexDirection: 'row',
              columnGap: 14,
            }}
          >
            <Icon name="ArrowNarrowLeft" height={20} width={20} />
            <Text
              style={{
                textTransform: 'uppercase',
                colors: colors.onNeutral,
                letterSpacing: '1.5%',
              }}
            >
              {backText}
            </Text>
          </TouchableOpacity>

          <ShimmerPlaceholder
            style={{
              height: 150,
              width: '100%',
              marginTop: spacing.spacing5,
              borderRadius: spacing.spacing5,
            }}
          />

          <View style={{ flexDirection: 'row', width: '100%', gap: 24 }}>
            <ShimmerPlaceholder
              style={{
                height: 800,
                marginTop: spacing.spacing5,
                borderRadius: spacing.spacing5,
                flex: 1,
              }}
            />
          </View>
        </View>
      </View>
    )
  }

  const programName = opportunities?.ProgrammeName__c
    ? `${opportunities?.ProgrammeName__c} ${
        opportunities?.Delivery_Mode__c
          ? `, ${opportunities?.Delivery_Mode__c}`
          : ''
      }`
    : ''

  return (
    <ScrollView
      style={{
        backgroundColor: colors.backgroundPrimary,
        paddingHorizontal: 24,
      }}
      contentContainerStyle={{
        marginVertical: 57,
        flexGrow: 1,
      }}
      ref={scrollViewRef}
    >
      <TouchableOpacity
        onPress={() => {
          if (Platform.OS === 'web') {
            if (isDashboard) {
              window.history.pushState({}, '', '/dashboard')
            } else {
              window.history.pushState({}, '', '/my-application')
            }
            window.location.reload()
          } else {
            navigation.navigate(isDashboard ? 'dashboard' : 'my-application')
          }
        }}
        style={{
          marginBottom: !tablet && spacing.spacing6,
          flexDirection: 'row',
          columnGap: 14,
        }}
      >
        <Icon name="ArrowNarrowLeft" height={20} width={20} />
        <Text
          style={{
            textTransform: 'uppercase',
            colors: colors.onNeutral,
            letterSpacing: '1.5%',
          }}
        >
          {backText}
        </Text>
      </TouchableOpacity>

      <TitleHeader style={{ paddingHorizontal: 24 }}>
        <View style={{ flexDirection: 'row' }}>
          <Text
            variant="heading3"
            color={colors.white}
            style={{
              alignSelf: 'center',
            }}
          >
            {programName || opportunities?.Name?.split('_')[0]} -{' '}
            <span> {opportunities?.Institution}</span>
          </Text>
        </View>
      </TitleHeader>
      {isTokensFetching ? (
        <MobileApplicationLoadingState />
      ) : (
        <View style={styles.container}>
          <OapPortal
            portalUrl={getOAPUrl(opportunities)}
            redirectPath="/application-filter"
            opportunityDetails={opportunities}
            userType={
              opportunities?.ApplicationSource__c?.toLowerCase() === 'oap'
                ? 'student'
                : 'agent'
            }
            applicationStatus={
              opportunities?.ApplicationStatus__c ? 'new' : 'draft'
            }
            userEmail={opportunities?.Account?.PersonEmail || ''}
            tokens={tokens}
            handleSubmissionConfirmation={(data) => {
              if (data) {
                console.log('Submission confirmed:', data)
                setSubmissionConfirmed(true)
                updateOpportunityAfterSubmission()
                setCountdown(30) // Reset countdown when submission is confirmed
                // Scroll to bottom to show the confirmation message
                setTimeout(() => {
                  scrollViewRef.current?.scrollToEnd({ animated: true })
                }, 100)
              }
            }}
          />
          {submissionConfirmed && (
            <View style={styles.confirmationMessage}>
              <Text
                variant="display4"
                color={colors.neutral}
                style={{ textAlign: 'center', marginBottom: 8 }}
              >
                You will redirected to My application page in{' '}
                <Text
                  style={{
                    color: colors.primary,
                    fontWeight: '600',
                    textDecorationLine: 'underline',
                  }}
                >
                  {countdown}
                </Text>
                <Text
                  style={{
                    color: colors.primary,
                    fontWeight: '600',
                    paddingLeft: 4,
                  }}
                  variant="display4"
                >
                  sec
                </Text>
              </Text>
              <Text
                variant="display4"
                color={colors.neutral}
                style={{ textAlign: 'center', marginBottom: 8 }}
              >
                or
              </Text>
              <TouchableOpacity
                onPress={() => {
                  if (Platform.OS === 'web') {
                    window.history.pushState({}, '', '/my-application')
                    window.location.reload()
                  } else {
                    navigation.navigate('my-application')
                  }
                }}
                variant="display4"
                style={styles.redirectLink}
              >
                Click here to go to
                <Text
                  variant="display4"
                  color={colors.primary}
                  style={{
                    textAlign: 'center',
                    textDecorationLine: 'underline',
                    fontWeight: '600',
                  }}
                >
                  My application page →
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      )}
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    marginTop: 24,
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  programHeader: {
    backgroundColor: '#2C3E50',
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  mainContent: {
    flex: 1,
    flexDirection: 'row',
  },
  sidebar: {
    width: 280,
    backgroundColor: '#4A90E2',
    paddingVertical: 24,
  },
  brandContainer: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  brandLogo: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  progressContainer: {
    marginTop: 8,
  },
  progressBar: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#FFFFFF',
    borderRadius: 2,
  },
  stepsList: {
    paddingHorizontal: 24,
    flex: 1,
  },
  stepItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  stepIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  stepNumber: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepLabel: {
    flex: 1,
    fontSize: 12,
  },
  languageSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.2)',
  },
  formContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  formScrollView: {
    flex: 1,
    paddingHorizontal: 40,
    paddingVertical: 32,
  },
  formTitle: {
    marginBottom: 32,
    color: '#2C3E50',
  },
  fieldContainer: {
    marginBottom: 24,
    position: 'relative',
  },
  fieldLabel: {
    marginBottom: 8,
    color: '#2C3E50',
    fontSize: 14,
  },
  required: {
    color: '#D72C2C',
  },
  dropdown: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
  },
  fieldIcon: {
    position: 'absolute',
    right: 12,
    top: 36,
  },
  buttonContainer: {
    marginTop: 32,
    alignItems: 'flex-start',
  },
  nextButton: {
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 4,
  },
  disabledButton: {
    opacity: 0.5,
  },
  loadingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 12,
  },
  confirmationMessage: {
    marginTop: 40,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  redirectLink: {
    paddingHorizontal: 8,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    fontSize: 14,
  },
})

export default MobileView
