// Brand-specific iframe configuration
const BRAND_IFRAME_CONFIG = {
  // GIH (Gisma University of Applied Sciences) configuration
  GIH: {
    type: 'CUSTOM_API',
    dev: {
      baseUrl: 'https://ghs.app-form.richbrains.net',
      apiEndpoint: '/iframe/application/create',
      authMethod: 'JWT_TOKEN',
    },
    prod: {
      baseUrl: 'https://gih.apphero.io',
      apiEndpoint: '/api/application/create',
      authMethod: 'JWT_TOKEN',
    },
  },
  // Berlin School of Business and Innovation configuration
  'Berlin School of Business and Innovation': {
    type: 'CUSTOM_API',
    dev: {
      baseUrl: 'https://bsbi-dev.apphero.io',
      apiEndpoint: '/api/application/create',
      authMethod: 'JWT_TOKEN',
    },
    prod: {
      baseUrl: 'https://bsbi.apphero.io',
      apiEndpoint: '/api/application/create',
      authMethod: 'JWT_TOKEN',
    },
  },
  // Arden University configuration
  'Arden University': {
    type: 'CUSTOM_API',
    dev: {
      baseUrl: 'https://arden-dev.apphero.io',
      apiEndpoint: '/api/application/create',
      authMethod: 'JWT_TOKEN',
    },
    prod: {
      baseUrl: 'https://arden.apphero.io',
      apiEndpoint: '/api/application/create',
      authMethod: 'JWT_TOKEN',
    },
  },
  // University of Europe for Applied Sciences configuration
  'University of Europe for Applied Sciences': {
    type: 'CUSTOM_API',
    dev: {
      baseUrl: 'https://ueas-dev.apphero.io',
      apiEndpoint: '/api/application/create',
      authMethod: 'JWT_TOKEN',
    },
    prod: {
      baseUrl: 'https://ueas.apphero.io',
      apiEndpoint: '/api/application/create',
      authMethod: 'JWT_TOKEN',
    },
  },
}

// Legacy OAP URL configuration for existing brands
const OAP_URL = {
  UCW_AGENT_PORTAL_DEV: 'https://ucw-oap-dev.apphero.io',
  UCW_OAP_DEV: 'https://apply-dev.ucanwest.ca',
}

/**
 * Get brand-specific iframe configuration
 * @param {Object} opportunity - The opportunity object
 * @returns {Object|null} Brand configuration or null if not found
 */
export const getBrandIframeConfig = (opportunity) => {
  if (!opportunity?.BusinessUnitFilter__c) return null

  const brandConfig = BRAND_IFRAME_CONFIG[opportunity.BusinessUnitFilter__c]
  if (!brandConfig) return null

  const stage = process.env.REACT_APP_STAGE?.toLowerCase() || 'dev'
  return {
    ...brandConfig,
    config: brandConfig[stage] || brandConfig.dev,
  }
}

/**
 * Check if a brand uses custom iframe workflow
 * @param {Object} opportunity - The opportunity object
 * @returns {boolean} True if brand uses custom workflow
 */
export const usesCustomIframeWorkflow = (opportunity) => {
  const brandConfig = getBrandIframeConfig(opportunity)
  return brandConfig?.type === 'CUSTOM_API'
}

export const getOAPUrl = (opporunity) => {
  if (
    !opporunity ||
    !opporunity?.BusinessUnitFilter__c ||
    !opporunity?.ApplicationSource__c
  )
    return ''

  // Check if this brand uses custom iframe workflow
  if (usesCustomIframeWorkflow(opporunity)) {
    const brandConfig = getBrandIframeConfig(opporunity)
    return brandConfig?.config?.baseUrl || ''
  }

  // Use legacy URL mapping for existing brands
  const urlKey = `${
    opporunity.BusinessUnitFilter__c
  }_${opporunity?.ApplicationSource__c?.replace(' ', '_').toUpperCase()}`
  return OAP_URL[`${urlKey}_${process.env.REACT_APP_STAGE?.toUpperCase()}`]
}
