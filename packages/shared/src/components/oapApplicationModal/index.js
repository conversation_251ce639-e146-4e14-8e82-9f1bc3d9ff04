import { View, StyleSheet, useWindowDimensions, Animated } from 'react-native'
import React, { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Button, Text } from '@libs/components'
import { generateOapAuthPayload } from '../../utils'
import OapPortalMobile from './index.mobile'
import CustomApiIframe from '../customApiIframe'
import { usesCustomIframeWorkflow } from '../../utils/getOAPUrl'

const OapPortal = ({
  portalUrl,
  messagePayload,
  onClose = () => {},
  opportunityDetails,
  userType = 'student',
  applicationStatus = 'new',
  userEmail,
  tokens,
  handleSubmissionConfirmation = () => {},
}) => {
  const { t } = useTranslation()
  const iframeRef = useRef(null)
  const { width } = useWindowDimensions()
  const [isLoading, setIsLoading] = useState(true)
  const [errorMessage, setErrorMessage] = useState('')

  // Animation refs for creative loader
  const pulseAnim = useRef(new Animated.Value(0.6)).current
  const rotateAnim = useRef(new Animated.Value(0)).current
  const scaleAnim = useRef(new Animated.Value(1)).current

  // Initialize animations
  useEffect(() => {
    if (isLoading) {
      // Pulse animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1200,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 0.6,
            duration: 1200,
            useNativeDriver: true,
          }),
        ]),
      ).start()

      // Rotation animation
      Animated.loop(
        Animated.timing(rotateAnim, {
          toValue: 1,
          duration: 3000,
          useNativeDriver: true,
        }),
      ).start()

      // Scale animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.1,
            duration: 1500,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
          }),
        ]),
      ).start()
    }
  }, [isLoading, pulseAnim, rotateAnim, scaleAnim])

  useEffect(() => {
    const sendTokensToIframe = async () => {
      try {
        setIsLoading(true)

        localStorage.setItem(
          'redirect-to-oap',
          JSON.stringify({
            enable: true,
            oapName: messagePayload?.oapName,
          }),
        )

        // Wait a bit for the iframe to render after component mounts
        await new Promise((resolve) => {
          setTimeout(resolve, 500)
        })

        // Wait for the iframe to be available in the DOM
        const waitForIframe = () =>
          new Promise((resolve, reject) => {
            let attempts = 0
            const maxAttempts = 50 // 5 seconds total

            const checkIframe = () => {
              const iframe = document.getElementById('oap-portal-iframe')
              if (iframe) {
                resolve(iframe)
              } else if (attempts >= maxAttempts) {
                reject(new Error('Iframe not found after waiting'))
              } else {
                attempts += 1
                setTimeout(checkIframe, 100)
              }
            }

            checkIframe()
          })

        const iframe = await waitForIframe()

        // Safety check for portalUrl
        if (!portalUrl) {
          throw new Error('Portal URL is not defined')
        }

        const targetOrigin = new URL(portalUrl).origin

        // Function to send authentication message to iframe
        const sendAuthMessage = () => {
          // Use the dynamic payload generation utility
          let message

          if (opportunityDetails && userEmail && tokens) {
            // Generate dynamic payload based on user type and status
            try {
              message = generateOapAuthPayload(
                opportunityDetails,
                userType,
                applicationStatus,
                tokens,
                userEmail,
              )
            } catch (error) {
              console.warn(
                'Failed to generate dynamic payload, falling back to messagePayload:',
                error,
              )
              message = messagePayload
            }
          }

          console.log('Sending message to OAP Portal:', targetOrigin)

          if (iframe && iframe.contentWindow) {
            iframe.contentWindow.postMessage(message, targetOrigin)
            // Keep loading until iframe confirms it's ready
          } else {
            setErrorMessage('Cannot access Oap Portal. Please try again.')
            setIsLoading(false)
          }
        }

        const messageHandler = (event) => {
          // Process messages from the iframe
          if (event.data && typeof event.data === 'object') {
            if (event.data.type === 'ready_state') {
              setIsLoading(false) // Only stop loading when iframe is ready
              sendAuthMessage()
            }
          }
        }

        window.addEventListener('message', messageHandler)

        // Add a load event listener to the iframe
        const iframeLoadHandler = () => {
          // Stop loading immediately when iframe loads
          setIsLoading(false)
          sendAuthMessage()

          // Remove the event listener after it's been triggered
          iframe.removeEventListener('load', iframeLoadHandler)
        }

        // Add the load event listener to the iframe
        iframe.addEventListener('load', iframeLoadHandler)

        // If the iframe is already loaded, trigger the handler manually
        if (
          iframe.contentDocument &&
          iframe.contentDocument.readyState === 'complete'
        ) {
          iframeLoadHandler()
        }

        // Only keep a minimal safety timeout as absolute fallback (in case load event never fires)
        const safetyTimeout = setTimeout(() => {
          setIsLoading(false)
        }, 10000) // 10 seconds safety net

        iframe.safetyTimeoutId = safetyTimeout
      } catch (error) {
        setErrorMessage(`Failed to initialize Oap Portal: ${error.message}`)
        setIsLoading(false)
      }
    }

    sendTokensToIframe()

    return () => {
      const iframe = document.getElementById('oap-portal-iframe')
      if (iframe) {
        iframe.removeEventListener('load', () => {})
        // Clear safety timeout if it exists
        if (iframe.safetyTimeoutId) {
          clearTimeout(iframe.safetyTimeoutId)
        }
      }
      // Remove message listener
      window.removeEventListener('message', () => {})
    }
  }, [messagePayload, portalUrl, tokens, width])

  useEffect(() => {
    // Set up message listener for submission confirmation
    const handleMessage = (event) => {
      if (event.data && event.data.type === 'SUBMISSION_CONFIRMATION') {
        handleSubmissionConfirmation(event.data)
      }
    }

    window.addEventListener('message', handleMessage)

    return () => {
      window.removeEventListener('message', handleMessage)
    }
  }, [handleSubmissionConfirmation])

  if (usesCustomIframeWorkflow(opportunityDetails)) {
    return (
      <CustomApiIframe
        opportunityDetails={opportunityDetails}
        userType={userType}
        applicationStatus={applicationStatus}
        userEmail={userEmail}
        tokens={tokens}
        onClose={onClose}
        handleSubmissionConfirmation={handleSubmissionConfirmation}
      />
    )
  }

  if (width < 650) {
    return (
      <OapPortalMobile
        portalUrl={portalUrl}
        messagePayload={messagePayload}
        onClose={onClose}
        opportunityDetails={opportunityDetails}
        userType={userType}
        applicationStatus={applicationStatus}
        userEmail={userEmail}
        tokens={tokens}
        handleSubmissionConfirmation={handleSubmissionConfirmation}
      />
    )
  }

  return (
    <View style={styles.container}>
      {(() => {
        if (errorMessage) {
          return (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{errorMessage}</Text>
              <Button
                label={t('Oap_PORTAL.CLOSE', 'Close')}
                onPress={onClose}
                buttonStyle={styles.errorButton}
              />
            </View>
          )
        }
        return (
          <View style={styles.contentContainer}>
            <View style={styles.iframeContainer}>
              <iframe
                id="oap-portal-iframe"
                ref={iframeRef}
                src={`${portalUrl}?fromAppHero=true`}
                style={styles.iframe}
                title="Oap Portal"
              />
            </View>
            {isLoading && (
              <View style={styles.loadingOverlay}>
                <View style={styles.loadingContent}>
                  {/* Creative animated loader */}
                  <View style={styles.loaderContainer}>
                    {/* Outer rotating ring */}
                    <Animated.View
                      style={[
                        styles.outerRing,
                        {
                          transform: [
                            {
                              rotate: rotateAnim.interpolate({
                                inputRange: [0, 1],
                                outputRange: ['0deg', '360deg'],
                              }),
                            },
                          ],
                        },
                      ]}
                    />

                    {/* Inner pulsing circle */}
                    <Animated.View
                      style={[
                        styles.innerCircle,
                        {
                          opacity: pulseAnim,
                          transform: [{ scale: scaleAnim }],
                        },
                      ]}
                    >
                      <View style={styles.portalIcon}>
                        <Text style={styles.portalIconText}>🌐</Text>
                      </View>
                    </Animated.View>

                    {/* Floating dots */}
                    {[0, 1, 2].map((index) => (
                      <Animated.View
                        key={index}
                        style={[
                          styles.floatingDot,
                          {
                            opacity: pulseAnim,
                            transform: [
                              {
                                rotate: rotateAnim.interpolate({
                                  inputRange: [0, 1],
                                  outputRange: [
                                    `${index * 120}deg`,
                                    `${index * 120 + 360}deg`,
                                  ],
                                }),
                              },
                            ],
                          },
                        ]}
                      />
                    ))}
                  </View>

                  {/* Loading text */}
                  <Animated.View
                    style={[styles.textContainer, { opacity: pulseAnim }]}
                  >
                    <Text style={styles.loadingText}>Connecting to Portal</Text>
                    <Text style={styles.loadingSubText}>
                      Setting up secure connection...
                    </Text>
                  </Animated.View>
                </View>
              </View>
            )}
          </View>
        )
      })()}
    </View>
  )
}

const styles = StyleSheet.create({
  content: {
    width: '100%',
    height: '100%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
    backgroundColor: '#ffffff',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 5,
  },
  loadingText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2563eb',
    marginBottom: 8,
    textAlign: 'center',
  },
  loadingSubText: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: 'red',
    textAlign: 'center',
    marginBottom: 20,
  },
  errorButton: {
    marginTop: 20,
    minWidth: 120,
  },
  container: {
    flex: 1,
    minHeight: '596px',
  },
  contentContainer: {
    flex: 1,
    position: 'relative',
  },
  iframeContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  iframe: {
    width: '100%',
    height: '100%',
    border: 'none',
    borderRadius: 12,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
    display: 'flex',
  },
  loadingContent: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    width: '100%',
  },
  loaderContainer: {
    position: 'relative',
    width: 120,
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
  },
  outerRing: {
    position: 'absolute',
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 3,
    borderColor: '#3b82f6',
    borderStyle: 'dashed',
    borderTopColor: 'transparent',
    borderRightColor: 'transparent',
  },
  innerCircle: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#eff6ff',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#3b82f6',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  portalIcon: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  portalIconText: {
    fontSize: 28,
  },
  floatingDot: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#3b82f6',
    top: 10,
    left: '50%',
    marginLeft: -4,
    transformOrigin: '4px 46px',
  },
  textContainer: {
    alignItems: 'center',
  },
})

export default OapPortal
