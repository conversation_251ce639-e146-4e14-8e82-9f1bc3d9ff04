import React, { useState, useEffect, useRef } from 'react'
import { View, StyleSheet, Animated, useWindowDimensions } from 'react-native'
import { useTranslation } from 'react-i18next'
import { Text } from '@libs/components'
import { useTheme } from '@libs/theme'
import { getBrandIframeConfig } from '../../utils/getOAPUrl'

/**
 * Custom API Iframe Component for Mobile - brands that use custom API integration
 * This component handles the specific workflow for brands like GIH, Berlin School, etc.
 */
const CustomApiIframeMobile = ({
  opportunityDetails,
  userType = 'student',
  applicationStatus = 'new',
  userEmail,
  tokens,
  onClose = () => {},
  handleSubmissionConfirmation = () => {},
}) => {
  const { t } = useTranslation()
  const { colors } = useTheme()
  const { width } = useWindowDimensions()
  const iframeRef = useRef(null)
  const [isLoading, setIsLoading] = useState(true)
  const [errorMessage, setErrorMessage] = useState('')

  // Animation refs for loader
  const pulseAnim = useRef(new Animated.Value(0.6)).current
  const rotateAnim = useRef(new Animated.Value(0)).current

  // Get brand-specific configuration
  const brandConfig = getBrandIframeConfig(opportunityDetails)

  useEffect(() => {
    if (!brandConfig) {
      setErrorMessage('Brand configuration not found')
      setIsLoading(false)
      return
    }

    // Start loading animations
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 0.6,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
    )

    const rotateAnimation = Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: true,
      }),
    )

    pulseAnimation.start()
    rotateAnimation.start()

    return () => {
      pulseAnimation.stop()
      rotateAnimation.stop()
    }
  }, [brandConfig, pulseAnim, rotateAnim])

  useEffect(() => {
    const initializeCustomApiIframeMobile = async () => {
      try {
        if (!brandConfig?.config) {
          throw new Error('Invalid brand configuration')
        }

        setIsLoading(true)

        // Store brand-specific configuration in localStorage
        localStorage.setItem(
          'custom-api-config-mobile',
          JSON.stringify({
            enable: true,
            brandName: opportunityDetails?.BusinessUnitFilter__c,
            apiEndpoint: brandConfig.config.apiEndpoint,
            authMethod: brandConfig.config.authMethod,
            isMobile: true,
          }),
        )

        // Wait for iframe to be available
        await new Promise((resolve) => setTimeout(resolve, 500))

        const waitForIframe = () =>
          new Promise((resolve, reject) => {
            let attempts = 0
            const maxAttempts = 50

            const checkIframe = () => {
              const iframe = document.getElementById('custom-api-iframe-mobile')
              if (iframe) {
                resolve(iframe)
              } else if (attempts >= maxAttempts) {
                reject(new Error('Custom API iframe not found'))
              } else {
                attempts += 1
                setTimeout(checkIframe, 100)
              }
            }

            checkIframe()
          })

        const iframe = await waitForIframe()
        const targetOrigin = new URL(brandConfig.config.baseUrl).origin

        // Prepare custom API message payload for mobile
        const customApiMessage = {
          type: 'CUSTOM_API_AUTH_MOBILE',
          brandName: opportunityDetails?.BusinessUnitFilter__c,
          opportunityId: opportunityDetails?.Id,
          userDetails: {
            email: userEmail,
            userType,
            applicationStatus,
          },
          tokens: tokens,
          apiConfig: {
            endpoint: brandConfig.config.apiEndpoint,
            authMethod: brandConfig.config.authMethod,
          },
          deviceInfo: {
            isMobile: true,
            screenWidth: width,
          },
          timestamp: new Date().toISOString(),
        }

        const sendCustomApiMessage = () => {
          console.log('Sending custom API message (mobile):', targetOrigin)

          if (iframe && iframe.contentWindow) {
            iframe.contentWindow.postMessage(customApiMessage, targetOrigin)
          } else {
            setErrorMessage(
              'Cannot access Custom API Portal. Please try again.',
            )
            setIsLoading(false)
          }
        }

        // Set up message handler for custom API responses
        const messageHandler = (event) => {
          if (event.data && typeof event.data === 'object') {
            switch (event.data.type) {
              case 'custom_api_ready_mobile':
              case 'custom_api_ready':
                setIsLoading(false)
                sendCustomApiMessage()
                break
              case 'CUSTOM_API_SUBMISSION_CONFIRMATION':
                handleSubmissionConfirmation(event.data)
                break
              case 'custom_api_error':
                setErrorMessage(
                  event.data.message || 'Custom API error occurred',
                )
                setIsLoading(false)
                break
              default:
                break
            }
          }
        }

        window.addEventListener('message', messageHandler)

        // Add load event listener
        const iframeLoadHandler = () => {
          setIsLoading(false)
          sendCustomApiMessage()
          iframe.removeEventListener('load', iframeLoadHandler)
        }

        iframe.addEventListener('load', iframeLoadHandler)

        // Safety timeout - longer for mobile
        const safetyTimeout = setTimeout(() => {
          setIsLoading(false)
        }, 20000) // 20 seconds for mobile custom API

        iframe.safetyTimeoutId = safetyTimeout

        return () => {
          window.removeEventListener('message', messageHandler)
          if (iframe.safetyTimeoutId) {
            clearTimeout(iframe.safetyTimeoutId)
          }
        }
      } catch (error) {
        setErrorMessage(
          `Failed to initialize Custom API Portal: ${error.message}`,
        )
        setIsLoading(false)
      }
    }

    if (brandConfig) {
      initializeCustomApiIframeMobile()
    }
  }, [
    brandConfig,
    opportunityDetails,
    userEmail,
    userType,
    applicationStatus,
    tokens,
    handleSubmissionConfirmation,
    width,
  ])

  if (!brandConfig) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>
          Brand configuration not available for{' '}
          {opportunityDetails?.BusinessUnitFilter__c}
        </Text>
      </View>
    )
  }

  if (errorMessage) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{errorMessage}</Text>
      </View>
    )
  }

  return (
    <View style={styles.container}>
      <View style={styles.iframeContainer}>
        <iframe
          id="custom-api-iframe-mobile"
          ref={iframeRef}
          src={`${
            brandConfig.config.baseUrl
          }?fromAppHero=true&brand=${encodeURIComponent(
            opportunityDetails?.BusinessUnitFilter__c,
          )}&mobile=true`}
          style={styles.iframe}
          title={`${opportunityDetails?.BusinessUnitFilter__c} Portal`}
        />
      </View>
      {isLoading && (
        <View style={styles.loadingOverlay}>
          <View style={styles.loadingContent}>
            <View style={styles.loaderContainer}>
              <Animated.View
                style={[
                  styles.outerRing,
                  {
                    transform: [
                      {
                        rotate: rotateAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: ['0deg', '360deg'],
                        }),
                      },
                    ],
                  },
                ]}
              />
              <Animated.View
                style={[
                  styles.innerCircle,
                  {
                    opacity: pulseAnim,
                    transform: [{ scale: pulseAnim }],
                  },
                ]}
              />
            </View>
            <Text style={styles.loadingText}>
              {t('LOADING.INITIALIZING_PORTAL', {
                brand: opportunityDetails?.BusinessUnitFilter__c,
              })}
            </Text>
            <Text style={styles.loadingSubText}>
              {t('LOADING.MOBILE_OPTIMIZED')}
            </Text>
          </View>
        </View>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  iframeContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  iframe: {
    width: '100%',
    height: '100%',
    border: 'none',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  loadingContent: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  loaderContainer: {
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  outerRing: {
    position: 'absolute',
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 3,
    borderColor: '#007AFF',
    borderTopColor: 'transparent',
  },
  innerCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#007AFF',
  },
  loadingText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    marginTop: 10,
  },
  loadingSubText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 5,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#FF3B30',
    textAlign: 'center',
  },
})

export default CustomApiIframeMobile
