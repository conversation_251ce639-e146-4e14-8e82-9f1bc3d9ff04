# Brand-Specific Iframe Integration

This document explains the brand-specific iframe integration system implemented for handling different workflow requirements based on opportunity BusinessUnit values.

## Overview

The system provides conditional iframe behavior where certain brands (like GIH, Berlin School of Business and Innovation, Arden University, etc.) use a custom API workflow, while other brands continue to use the existing legacy workflow.

## Architecture

### Components

1. **CustomApiIframe** (`index.js`) - Desktop version for custom API brands
2. **CustomApiIframeMobile** (`index.mobile.js`) - Mobile version for custom API brands
3. **OapPortal** - Updated to conditionally use custom or legacy workflow
4. **OapPortalMobile** - Updated to conditionally use custom or legacy workflow

### Configuration

Brand-specific configurations are defined in `packages/shared/src/utils/getOAPUrl.js`:

```javascript
const BRAND_IFRAME_CONFIG = {
  GIH: {
    type: 'CUSTOM_API',
    dev: {
      baseUrl: 'https://gih-dev.apphero.io',
      apiEndpoint: '/api/application/create',
      authMethod: 'JWT_TOKEN',
    },
    prod: {
      baseUrl: 'https://gih.apphero.io',
      apiEndpoint: '/api/application/create',
      authMethod: 'JWT_TOKEN',
    },
  },
  // ... other brands
}
```

## Supported Brands

### Custom API Workflow Brands

- **GIH** (Gisma University of Applied Sciences)
- **Berlin School of Business and Innovation**
- **Arden University**
- **University of Europe for Applied Sciences**

### Legacy Workflow Brands

- **UCW** and all other existing brands continue to use the original workflow

## Usage

The system automatically detects the brand and applies the appropriate workflow:

```javascript
// In OapPortal component
if (usesCustomIframeWorkflow(opportunityDetails)) {
  return <CustomApiIframe {...props} />
}
// Otherwise use legacy workflow
```

## API Integration

### Custom API Message Format

Custom API brands receive a specialized message payload:

```javascript
{
  type: 'CUSTOM_API_AUTH',
  brandName: 'GIH',
  opportunityId: 'opportunity-id',
  userDetails: {
    email: '<EMAIL>',
    userType: 'student',
    applicationStatus: 'new'
  },
  tokens: { /* authentication tokens */ },
  apiConfig: {
    endpoint: '/api/application/create',
    authMethod: 'JWT_TOKEN'
  },
  timestamp: '2024-01-01T00:00:00.000Z'
}
```

### Expected Response Messages

Custom API iframes should respond with:

- `custom_api_ready` - When iframe is ready to receive auth message
- `CUSTOM_API_SUBMISSION_CONFIRMATION` - When application is submitted
- `custom_api_error` - When an error occurs

## Configuration Management

### Environment-Specific URLs

The system supports different URLs for development and production environments:

```javascript
const stage = process.env.REACT_APP_STAGE?.toLowerCase() || 'dev'
const config = brandConfig[stage] || brandConfig.dev
```

### Adding New Brands

To add a new brand to the custom API workflow:

1. Add brand configuration to `BRAND_IFRAME_CONFIG` in `getOAPUrl.js`
2. Ensure the brand's iframe portal supports the custom API message format
3. Test with both development and production environments

## Testing

### Manual Testing

Use the validation utility:

```javascript
// Run comprehensive validation
const results = validateBrandIframeConfig()

// Or quick test in browser console
quickTest()
```

### Browser Console Testing

The validation functions are available in the browser console:

```javascript
// Test specific brand
window.quickTest()

// Full validation suite
window.validateBrandIframeConfig()
```

## Backward Compatibility

- All existing brands continue to work without changes
- Legacy workflow remains unchanged
- No breaking changes to existing API contracts

## Error Handling

The system includes comprehensive error handling:

- Configuration validation
- Iframe loading timeouts (15-20 seconds for custom API)
- Message handling errors
- Fallback error displays

## Security Considerations

- Authentication tokens are passed securely via postMessage
- Origin validation for message handling
- JWT token authentication for custom API brands
- Secure HTTPS endpoints for all brand configurations

## Monitoring and Debugging

### Console Logging

Custom API iframe components log key events:

- Configuration loading
- Message sending/receiving
- Error conditions
- Authentication flow

### LocalStorage Debugging

Configuration is stored in localStorage for debugging:

- `custom-api-config` - Desktop configuration
- `custom-api-config-mobile` - Mobile configuration

## Future Enhancements

- Dynamic configuration loading from API
- Brand-specific UI customization
- Enhanced analytics and monitoring
- Additional authentication methods
