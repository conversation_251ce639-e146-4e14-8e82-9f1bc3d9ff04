import React, { useState, useEffect, useRef } from 'react'
import { View, StyleSheet, Animated, useWindowDimensions } from 'react-native'
import { useTranslation } from 'react-i18next'
import CryptoJS from 'crypto-js'
import { Text } from '@libs/components'
import { useTheme } from '@libs/theme'
import { getBrandIframeConfig } from '../../utils/getOAPUrl'
import { proxyExternalApiCall } from '../../api'

// HMAC-SHA256 signature generation utility
const SECRET_KEY = 'jkHh*&(^789'

/**
 * Generate HMAC-SHA256 signature for data object
 * @param {Object} data - The data object to sign
 * @returns {string} Base64 encoded signature
 */
function generateSignedData(dataObject, secretKey = SECRET_KEY) {
  try {
    // Convert data object to JSON string
    const dataJson = JSON.stringify(dataObject)

    // Base64 encode the JSON data using browser's btoa
    const base64EncodedData = btoa(dataJson)

    // Generate HMAC-SHA256 signature using crypto-js
    const signature = CryptoJS.HmacSHA256(base64EncodedData, secretKey)

    // Base64 encode the signature
    const base64EncodedSignature = signature.toString(CryptoJS.enc.Base64)

    // Concatenate signature and data with a period
    const signedData = `${base64EncodedSignature}.${base64EncodedData}`

    return signedData
  } catch (error) {
    throw new Error(`Failed to generate signed data: ${error.message}`)
  }
}

/**
 * Custom API Iframe Component for brands that use custom API integration
 * This component handles the specific workflow for brands like GIH, Berlin School, etc.
 * Uses direct API calls with HMAC-SHA256 signature generation instead of iframe communication
 */
const CustomApiIframe = ({
  opportunityDetails,
  userType = 'student',
  applicationStatus = 'new',
  userEmail,
  tokens,
  onClose = () => {},
  handleSubmissionConfirmation = () => {},
}) => {
  const { t } = useTranslation()
  const iframeRef = useRef(null)
  const [isLoading, setIsLoading] = useState(true)
  const [errorMessage, setErrorMessage] = useState('')

  // Animation refs for loader
  const pulseAnim = useRef(new Animated.Value(0.6)).current
  const rotateAnim = useRef(new Animated.Value(0)).current

  // Get brand-specific configuration
  const brandConfig = getBrandIframeConfig(opportunityDetails)

  useEffect(() => {
    if (!brandConfig) {
      setErrorMessage('Brand configuration not found')
      setIsLoading(false)
      return
    }

    // Start loading animations
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 0.6,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
    )

    const rotateAnimation = Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: true,
      }),
    )

    pulseAnimation.start()
    rotateAnimation.start()

    return () => {
      pulseAnimation.stop()
      rotateAnimation.stop()
    }
  }, [brandConfig, pulseAnim, rotateAnim])

  useEffect(() => {
    const initializeCustomApiWithDirectCall = async () => {
      try {
        if (!brandConfig?.config) {
          throw new Error('Invalid brand configuration')
        }

        setIsLoading(true)

        const dateTime = new Date().toISOString().slice(0, 19).replace('T', ' ')

        console.log(opportunityDetails)

        // Create data object for API call
        const apiData = {
          dateTime,
          externalId: opportunityDetails.ApplicationFormId__c,
          clientId: 'apphero',
          sfProgramId: 'a016700001CkJUDAA3',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'Logesh',
          phone: '+************',
          country: 'India',
        }

        console.log('API Data:', JSON.stringify(apiData, null, 2))

        // Generate signature for the data
        const signature = generateSignedData(apiData)

        // Construct URL with signed data parameters
        const { baseUrl, apiEndpoint } = brandConfig.config
        const createApplicationUrl = `${baseUrl}${apiEndpoint}?signed_data=${encodeURIComponent(
          signature,
        )}`

        console.log('Custom API iframe URL:', iframeUrl)

        // Option 2: Use fetch with no-cors mode (limited response access)
        try {
          const response = await fetch(createApplicationUrl, {
            method: 'GET',
            mode: 'no-cors', // This bypasses CORS but limits response access
            headers: {
              'Content-Type': 'application/json',
            },
          })
          console.log('No-CORS Response status:', response)
          // Note: response.json() won't work with no-cors mode
        } catch (fetchError) {
          console.error('No-CORS fetch failed:', fetchError)
        }

        const updatePayload = {
          clientId: 'apphero',
          externalId: opportunityDetails.ApplicationFormId__c,
          dateTime,
        }

        const updateSignature = generateSignedData(updatePayload)

        // Wait for iframe to load and then update the src
        await new Promise((resolve) => setTimeout(resolve, 500))

        const waitForIframe = () =>
          new Promise((resolve, reject) => {
            let attempts = 0
            const maxAttempts = 1

            const checkIframe = () => {
              const iframe = document.getElementById('custom-api-iframe')
              if (iframe) {
                resolve(iframe)
              } else if (attempts >= maxAttempts) {
                reject(new Error('Custom API iframe not found'))
              } else {
                attempts += 1
                setTimeout(checkIframe, 100)
              }
            }

            checkIframe()
          })

        const iframe = await waitForIframe()

        // Update iframe src with signed data
        iframe.src = iframeUrl

        // Set up message handler for API responses
        const messageHandler = (event) => {
          if (event.data && typeof event.data === 'object') {
            switch (event.data.type) {
              case 'CUSTOM_API_SUBMISSION_CONFIRMATION':
                handleSubmissionConfirmation(event.data)
                break
              case 'custom_api_error':
                setErrorMessage(
                  event.data.message || 'Custom API error occurred',
                )
                setIsLoading(false)
                break
              default:
                break
            }
          }
        }

        window.addEventListener('message', messageHandler)

        // Add load event listener
        const iframeLoadHandler = () => {
          setIsLoading(false)
          iframe.removeEventListener('load', iframeLoadHandler)
        }

        iframe.addEventListener('load', iframeLoadHandler)

        // Safety timeout
        const safetyTimeout = setTimeout(() => {
          setIsLoading(false)
        }, 15000) // 15 seconds for custom API

        iframe.safetyTimeoutId = safetyTimeout

        return () => {
          window.removeEventListener('message', messageHandler)
          if (iframe.safetyTimeoutId) {
            clearTimeout(iframe.safetyTimeoutId)
          }
        }
      } catch (error) {
        setErrorMessage(
          `Failed to initialize Custom API Portal: ${error.message}`,
        )
        setIsLoading(false)
      }
    }

    if (brandConfig) {
      initializeCustomApiWithDirectCall()
    }
  }, [
    brandConfig,
    opportunityDetails,
    userEmail,
    userType,
    applicationStatus,
    tokens,
    handleSubmissionConfirmation,
  ])

  if (!brandConfig) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>
          Brand configuration not available for{' '}
          {opportunityDetails?.BusinessUnitFilter__c}
        </Text>
      </View>
    )
  }

  if (errorMessage) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{errorMessage}</Text>
      </View>
    )
  }

  return (
    <View style={styles.container}>
      <View style={styles.iframeContainer}>
        <iframe
          id="custom-api-iframe"
          ref={iframeRef}
          src="about:blank"
          style={styles.iframe}
          title={`${opportunityDetails?.BusinessUnitFilter__c} Portal`}
        />
      </View>
      {isLoading && (
        <View style={styles.loadingOverlay}>
          <View style={styles.loadingContent}>
            <View style={styles.loaderContainer}>
              <Animated.View
                style={[
                  styles.outerRing,
                  {
                    transform: [
                      {
                        rotate: rotateAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: ['0deg', '360deg'],
                        }),
                      },
                    ],
                  },
                ]}
              />
              <Animated.View
                style={[
                  styles.innerCircle,
                  {
                    opacity: pulseAnim,
                    transform: [{ scale: pulseAnim }],
                  },
                ]}
              />
            </View>
            <Text style={styles.loadingText}>
              {t('LOADING.INITIALIZING_PORTAL', {
                brand: opportunityDetails?.BusinessUnitFilter__c,
              })}
            </Text>
          </View>
        </View>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  iframeContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  iframe: {
    width: '100%',
    height: '100%',
    border: 'none',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  loadingContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  loaderContainer: {
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  outerRing: {
    position: 'absolute',
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 3,
    borderColor: '#007AFF',
    borderTopColor: 'transparent',
  },
  innerCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#007AFF',
  },
  loadingText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#FF3B30',
    textAlign: 'center',
  },
})

export default CustomApiIframe
